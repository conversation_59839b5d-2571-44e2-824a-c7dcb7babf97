# Импорт необходимых библиотек
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import warnings
from typing import Dict, List, Optional, Union, Tuple, Any
from scipy import stats

# Настройка отображения
plt.style.use("default")
plt.rcParams["figure.figsize"] = (12, 8)
plt.rcParams["font.size"] = 10
warnings.filterwarnings("ignore", category=FutureWarning)

print("Библиотеки успешно импортированы")

def load_and_validate_taxi_data(
    file_path: str,
    required_columns: Optional[List[str]] = None,
    time_columns: Optional[List[str]] = None,
    time_format: str = "%d.%m.%y %H:%M",
    validate_data: bool = True,
) -> pd.DataFrame:
    """
    Загружает и валидирует данные такси с автоматической обработкой временных колонок.

    Parameters:
    -----------
    file_path : str
        Путь к файлу с данными
    required_columns : List[str], optional
        Список обязательных колонок для проверки
    time_columns : List[str], optional
        Список временных колонок для преобразования
    time_format : str
        Формат времени для преобразования
    validate_data : bool
        Выполнять ли валидацию данных

    Returns:
    --------
    pd.DataFrame
        Загруженные и обработанные данные

    Raises:
    -------
    FileNotFoundError
        Если файл не найден
    ValueError
        При ошибках валидации данных
    """

    # Значения по умолчанию
    if required_columns is None:
        required_columns = [
            "id_order",
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
            "city",
        ]

    if time_columns is None:
        time_columns = [
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
        ]

    try:
        # Загрузка данных
        print(f"Загрузка данных из {file_path}...")

        if file_path.endswith(".xlsx") or file_path.endswith(".xls"):
            df = pd.read_excel(file_path)
        elif file_path.endswith(".csv"):
            df = pd.read_csv(file_path)
        else:
            raise ValueError(f"Неподдерживаемый формат файла: {file_path}")

        print(f"Загружено {len(df)} строк и {len(df.columns)} колонок")

        if validate_data:
            # Проверка наличия обязательных колонок
            missing_columns = set(required_columns) - set(df.columns)
            if missing_columns:
                raise ValueError(f"Отсутствуют обязательные колонки: {missing_columns}")
            print("✓ Все обязательные колонки присутствуют")

            # Проверка на дубликаты ID
            if df["id_order"].duplicated().any():
                warnings.warn("Обнаружены дублированные ID заказов")
            else:
                print("✓ Дубликаты ID не обнаружены")

        # Преобразование временных колонок
        for col in time_columns:
            if col in df.columns:
                try:
                    df[col] = pd.to_datetime(df[col], format=time_format)
                    print(f"✓ Колонка {col} преобразована в datetime")
                except Exception as e:
                    print(f"⚠ Ошибка преобразования колонки {col}: {e}")
                    # Попытка автоматического определения формата
                    try:
                        df[col] = pd.to_datetime(df[col], errors="coerce")
                        print(f"✓ Колонка {col} преобразована автоматически")
                    except:
                        print(f"✗ Не удалось преобразовать колонку {col}")

        # Добавление производных колонок
        if "order_time" in df.columns and df["order_time"].dtype == "datetime64[ns]":
            df["day_order"] = df["order_time"].dt.day
            df["hour_order"] = df["order_time"].dt.floor("h")
            print("✓ Добавлены производные временные колонки")

        # Статистика по пропущенным значениям
        missing_stats = df.isnull().sum()
        if missing_stats.sum() > 0:
            print("\nСтатистика пропущенных значений:")
            for col, count in missing_stats[missing_stats > 0].items():
                percent = (count / len(df)) * 100
                print(f"  {col}: {count} ({percent:.1f}%)")
        else:
            print("✓ Пропущенные значения не обнаружены")

        print("\nДанные успешно загружены и обработаны!")
        return df

    except Exception as e:
        print(f"Ошибка при загрузке данных: {e}")
        raise

def calculate_taxi_metrics(
    df: pd.DataFrame,
    group_by: Union[str, List[str]] = "day_order",
    metrics_config: Optional[Dict[str, str]] = None,
    conversion_metrics: bool = True,
) -> pd.DataFrame:
    """
    Рассчитывает метрики такси с настраиваемыми параметрами группировки и агрегации.

    Parameters:
    -----------
    df : pd.DataFrame
        Исходные данные такси
    group_by : str or List[str]
        Колонки для группировки данных
    metrics_config : Dict[str, str], optional
        Конфигурация метрик в формате {название_метрики: колонка_для_подсчета}
    conversion_metrics : bool
        Рассчитывать ли метрики конверсии

    Returns:
    --------
    pd.DataFrame
        Агрегированные данные с метриками
    """

    # Конфигурация метрик по умолчанию
    if metrics_config is None:
        metrics_config = {
            "cnt_order": "id_order",
            "cnt_offer": "offer_time",
            "cnt_assign": "assign_time",
            "cnt_arrive": "arrive_time",
            "cnt_trip": "trip_time",
        }

    print(f"Расчет метрик с группировкой по: {group_by}")
    print(f"Метрики для расчета: {list(metrics_config.keys())}")

    try:
        # Создание агрегации
        agg_dict = {}
        for metric_name, column in metrics_config.items():
            if column in df.columns:
                agg_dict[metric_name] = (column, "count")
            else:
                print(
                    f"⚠ Колонка {column} не найдена, пропускаем метрику {metric_name}"
                )

        # Группировка и агрегация
        df_grouped = df.groupby(group_by, as_index=False).agg(agg_dict)

        print(f"✓ Создано {len(df_grouped)} агрегированных записей")

        # Расчет метрик конверсии
        if conversion_metrics and len(agg_dict) >= 2:
            print("Расчет метрик конверсии...")

            # Базовые конверсии
            if "cnt_order" in df_grouped.columns and "cnt_trip" in df_grouped.columns:
                df_grouped["order2trip"] = (
                    df_grouped["cnt_trip"] / df_grouped["cnt_order"]
                )
                print("✓ order2trip (базовая конверсия)")

            if "cnt_order" in df_grouped.columns and "cnt_offer" in df_grouped.columns:
                df_grouped["order2offer"] = (
                    df_grouped["cnt_offer"] / df_grouped["cnt_order"]
                )
                print("✓ order2offer")

            if "cnt_offer" in df_grouped.columns and "cnt_assign" in df_grouped.columns:
                df_grouped["offer2assign"] = (
                    df_grouped["cnt_assign"] / df_grouped["cnt_offer"]
                )
                print("✓ offer2assign")

            if (
                "cnt_assign" in df_grouped.columns
                and "cnt_arrive" in df_grouped.columns
            ):
                df_grouped["assign2arrive"] = (
                    df_grouped["cnt_arrive"] / df_grouped["cnt_assign"]
                )
                print("✓ assign2arrive")

            if "cnt_arrive" in df_grouped.columns and "cnt_trip" in df_grouped.columns:
                df_grouped["arrive2trip"] = (
                    df_grouped["cnt_trip"] / df_grouped["cnt_arrive"]
                )
                print("✓ arrive2trip")

        # Обработка бесконечных значений и NaN
        df_grouped = df_grouped.replace([np.inf, -np.inf], np.nan)

        print(f"\nИтоговые колонки: {list(df_grouped.columns)}")
        return df_grouped

    except Exception as e:
        print(f"Ошибка при расчете метрик: {e}")
        raise

def plot_city_metrics(
    df_city: pd.DataFrame,
    metric_column: str,
    x_column: str = "day_order",
    cities: Optional[List[str]] = None,
    title: Optional[str] = None,
    y_limit: Optional[Tuple[float, float]] = None,
    figsize: Tuple[int, int] = (12, 8),
    style: str = "default",
    save_path: Optional[str] = None,
) -> None:
    """
    Автоматизированное построение графиков метрик по городам.

    Parameters:
    -----------
    df_city : pd.DataFrame
        Данные с метриками по городам
    metric_column : str
        Название колонки с метрикой для отображения
    x_column : str
        Колонка для оси X
    cities : List[str], optional
        Список городов для отображения. Если None, отображаются все
    title : str, optional
        Заголовок графика
    y_limit : Tuple[float, float], optional
        Ограничения по оси Y
    figsize : Tuple[int, int]
        Размер фигуры
    style : str
        Стиль графика
    save_path : str, optional
        Путь для сохранения графика
    """

    # Проверка наличия необходимых колонок
    required_cols = [metric_column, x_column, "city"]
    missing_cols = [col for col in required_cols if col not in df_city.columns]
    if missing_cols:
        raise ValueError(f"Отсутствуют колонки: {missing_cols}")

    # Определение городов для отображения
    if cities is None:
        cities = df_city["city"].unique().tolist()
        print(f"Отображение всех городов: {cities}")
    else:
        # Проверка наличия городов в данных
        available_cities = df_city["city"].unique()
        missing_cities = [city for city in cities if city not in available_cities]
        if missing_cities:
            print(f"⚠ Города не найдены в данных: {missing_cities}")
            cities = [city for city in cities if city in available_cities]

    if not cities:
        raise ValueError("Нет доступных городов для отображения")

    # Настройка стиля
    plt.style.use(style)

    # Создание графика
    plt.figure(figsize=figsize)

    # Цветовая палитра
    colors = plt.cm.Set1(np.linspace(0, 1, len(cities)))

    for i, city in enumerate(cities):
        city_data = df_city[df_city["city"] == city]

        if len(city_data) == 0:
            print(f"⚠ Нет данных для города: {city}")
            continue

        plt.plot(
            city_data[x_column],
            city_data[metric_column],
            label=city,
            color=colors[i],
            marker="o",
            linewidth=2,
            markersize=6,
        )

    # Настройка графика
    plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    plt.grid(True, alpha=0.3)

    if title:
        plt.title(title, fontsize=14, fontweight="bold")
    else:
        plt.title(f"{metric_column} по городам", fontsize=14, fontweight="bold")

    plt.xlabel(x_column.replace("_", " ").title(), fontsize=12)
    plt.ylabel(metric_column.replace("_", " ").title(), fontsize=12)

    if y_limit:
        plt.ylim(y_limit)

    plt.tight_layout()

    # Сохранение графика
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches="tight")
        print(f"График сохранен: {save_path}")

    plt.show()

    # Статистика по метрике
    print(f"\nСтатистика по метрике '{metric_column}':")
    for city in cities:
        city_data = df_city[df_city["city"] == city][metric_column]
        if len(city_data) > 0:
            print(
                f"  {city}: среднее={city_data.mean():.3f}, "
                f"мин={city_data.min():.3f}, макс={city_data.max():.3f}"
            )

def detect_outliers(
    df: pd.DataFrame,
    columns: Optional[List[str]] = None,
    method: str = "iqr",
    threshold: float = 1.5,
    z_threshold: float = 3.0,
    return_indices: bool = False,
) -> Union[pd.DataFrame, Dict[str, Any]]:
    """
    Автоматическое обнаружение выбросов в данных с различными методами.

    Parameters:
    -----------
    df : pd.DataFrame
        Данные для анализа
    columns : List[str], optional
        Колонки для анализа. Если None, анализируются все числовые колонки
    method : str
        Метод детекции: 'iqr', 'zscore', 'modified_zscore'
    threshold : float
        Пороговое значение для IQR метода
    z_threshold : float
        Пороговое значение для Z-score методов
    return_indices : bool
        Возвращать ли индексы выбросов

    Returns:
    --------
    Union[pd.DataFrame, Dict]
        Результаты детекции выбросов
    """

    if columns is None:
        # Автоматический выбор числовых колонок
        columns = df.select_dtypes(include=[np.number]).columns.tolist()
        print(f"Анализ выбросов для колонок: {columns}")

    results = {}
    all_outlier_indices = set()

    for col in columns:
        if col not in df.columns:
            print(f"⚠ Колонка {col} не найдена")
            continue

        data = df[col].dropna()
        if len(data) == 0:
            print(f"⚠ Колонка {col} не содержит данных")
            continue

        outlier_indices = set()

        if method == "iqr":
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            outlier_mask = (data < lower_bound) | (data > upper_bound)
            outlier_indices = set(data[outlier_mask].index)

        elif method == "zscore":
            z_scores = np.abs(stats.zscore(data))
            outlier_mask = z_scores > z_threshold
            outlier_indices = set(data[outlier_mask].index)

        elif method == "modified_zscore":
            median = np.median(data)
            mad = np.median(np.abs(data - median))
            modified_z_scores = 0.6745 * (data - median) / mad
            outlier_mask = np.abs(modified_z_scores) > z_threshold
            outlier_indices = set(data[outlier_mask].index)

        outlier_count = len(outlier_indices)
        outlier_percent = (outlier_count / len(data)) * 100

        results[col] = {
            "count": outlier_count,
            "percentage": outlier_percent,
            "indices": list(outlier_indices) if return_indices else None,
            "method": method,
            "threshold": threshold if method == "iqr" else z_threshold,
        }

        all_outlier_indices.update(outlier_indices)

        print(f"Колонка {col}: {outlier_count} выбросов ({outlier_percent:.1f}%)")

    # Общая статистика
    total_outliers = len(all_outlier_indices)
    total_percent = (total_outliers / len(df)) * 100

    summary = {
        "total_outliers": total_outliers,
        "total_percentage": total_percent,
        "method": method,
        "columns_analyzed": columns,
        "outlier_indices": list(all_outlier_indices) if return_indices else None,
    }

    print(
        f"\nОбщая статистика: {total_outliers} уникальных записей с выбросами ({total_percent:.1f}%)"
    )

    return {"summary": summary, "details": results}

# Загрузка данных с автоматической валидацией
df = load_and_validate_taxi_data("taxi_data.xlsx")

# Отображение основной информации о данных
print("\nОсновная информация о данных:")
print(f"Размер данных: {df.shape}")
print(f"Колонки: {list(df.columns)}")
print(f"Уникальные города: {df['city'].unique()}")
print(f"Период данных: с {df['order_time'].min()} по {df['order_time'].max()}")

# Отображение первых строк
df.head()

# Расчет метрик с группировкой по дням и городам
df_gr_dyn_city = calculate_taxi_metrics(df, group_by=["day_order", "city"])

print("\nМетрики по дням и городам:")
df_gr_dyn_city.head()

# График Количество заказов
plot_city_metrics(
    df_gr_dyn_city,
    metric_column="cnt_order",
    title="Количество заказов",
    cities=["Казань", "Москва", "Санкт-Петербург", "Краснодар"],
)

# График Order2Trip - Базовая конверсия
plot_city_metrics(
    df_gr_dyn_city,
    metric_column="order2trip",
    title="Order2Trip - Базовая конверсия",
    y_limit=(0, 1),
    cities=["Казань", "Москва", "Санкт-Петербург", "Краснодар"],
)

# График Order2Offer - Конверсия из заказа в предложение
plot_city_metrics(
    df_gr_dyn_city,
    metric_column="order2offer",
    title="Order2Offer - Конверсия из заказа в предложение",
    y_limit=(0, 1),
    cities=["Казань", "Москва", "Санкт-Петербург", "Краснодар"],
)

# График Offer2Assign - Конверсия из предложения в назначение
plot_city_metrics(
    df_gr_dyn_city,
    metric_column="offer2assign",
    title="Offer2Assign - Конверсия из предложения в назначение",
    y_limit=(0, 1),
    cities=["Казань", "Москва", "Санкт-Петербург", "Краснодар"],
)

# График Assign2Arrive - Конверсия из назначения в прибытие
plot_city_metrics(
    df_gr_dyn_city,
    metric_column="assign2arrive",
    title="Assign2Arrive - Конверсия из назначения в прибытие",
    y_limit=(0, 1),
    cities=["Казань", "Москва", "Санкт-Петербург", "Краснодар"],
)

# График Arrive2Trip - Конверсия из прибытия в завершение поездки
plot_city_metrics(
    df_gr_dyn_city,
    metric_column="arrive2trip",
    title="Arrive2Trip - Конверсия из прибытия в завершение поездки",
    y_limit=(0, 1),
    cities=["Казань", "Москва", "Санкт-Петербург", "Краснодар"],
)