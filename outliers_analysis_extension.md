# Анализ выбросов в метриках конверсии по городам

## Обзор реализации

Заменил анализ выбросов в файле `taxi_analysis_modular_refactored.ipynb` на анализ метрик конверсии:

## 1. Новые функции

### `detect_outliers_by_groups()`
- **Назначение**: Анализ выбросов с группировкой по заданному столбцу (например, по городам)
- **Параметры**:
  - `df`: DataFrame с данными
  - `metrics`: Список метрик для анализа (если None, анализируются все числовые столбцы)
  - `group_by`: Столбец для группировки (по умолчанию 'city')
  - `method`: Метод детекции ('iqr')
  - `threshold`: Пороговое значение для IQR (по умолчанию 1.5)
- **Возвращает**: Словарь с результатами анализа и сводной таблицей

### `visualize_outliers_by_groups()`
- **Назначение**: Визуализация результатов анализа выбросов
- **Создает**: 4 графика в одной фигуре:
  1. Тепловая карта количества выбросов
  2. Тепловая карта процента выбросов
  3. Столбчатая диаграмма по группам (городам)
  4. Столбчатая диаграмма по метрикам

### `print_outliers_summary_table()`
- **Назначение**: Вывод детальной сводной таблицы результатов
- **Включает**:
  - Таблицу с результатами по каждой метрике и группе
  - Общую статистику
  - Статистику по группам (городам)
  - Статистику по метрикам

## 2. Фокус на метриках конверсии

Анализ сосредоточен исключительно на метриках конверсии:

### Метрики конверсии (единственные анализируемые):
- `order2trip` - общая конверсия заказ → поездка
- `order2offer` - конверсия заказ → предложение
- `offer2assign` - конверсия предложение → назначение
- `assign2arrive` - конверсия назначение → прибытие
- `arrive2trip` - конверсия прибытие → поездка

### Обоснование выбора:
- **Стабильность**: Метрики конверсии стабильны во времени (в отличие от количественных метрик)
- **Индикативность**: Резкие изменения указывают на проблемы в системе
- **Практическая значимость**: Подходят для системы алертов
- **Бизнес-логика**: Отражают эффективность процессов, а не колебания спроса

## 3. Группировка по городам

- Выбросы определяются **относительно каждого города отдельно**
- Это важно, поскольку нормальные значения для крупного города могут быть выбросами для маленького
- Анализ проводится для каждой комбинации "метрика × город"

## 4. Использование IQR метода

- Применяется метод межквартильного размаха (IQR)
- Порог по умолчанию: 1.5 (стандартное значение)
- Выбросы: значения < Q1 - 1.5×IQR или > Q3 + 1.5×IQR

## 5. Структура реализации

### Секция: Анализ выбросов в метриках конверсии по городам
**Единственная ячейка с анализом**:
1. **Обоснование подхода** - объяснение почему конверсии лучше количественных метрик
2. **Определение метрик** - список из 5 метрик конверсии
3. **Анализ выбросов** - использование `detect_outliers_by_groups()` с группировкой по городам
4. **Сводная таблица** - вывод результатов через `print_outliers_summary_table()`
5. **Визуализация** - одна диаграмма через `visualize_outliers_by_groups()`

### Упрощение:
- Убраны метрики количества (cnt_*)
- Убран детальный анализ по городам
- Убран комплексный анализ
- Оставлена только практически значимая часть

## 6. Результаты анализа

Анализ предоставляет:

### Сводные таблицы:
- Количество и процент выбросов по каждой метрике и городу
- Общая статистика по всем данным
- Ранжированные списки городов и метрик по количеству выбросов

### Визуализация:
- **Тепловые карты** - показывают распределение выбросов по городам и метрикам
- **Столбчатые диаграммы** - сравнение городов и метрик по количеству выбросов
- **Цветовое кодирование** - для лучшего восприятия данных

### Детальная статистика:
- Границы выбросов (нижняя и верхняя) для каждой группы
- Процентное соотношение выбросов к общему количеству записей
- Топ-проблемные метрики для каждого города

## 7. Преимущества реализации

1. **Модульность** - функции можно использовать независимо
2. **Гибкость** - настраиваемые параметры для разных типов анализа
3. **Масштабируемость** - легко добавить новые метрики или изменить группировку
4. **Визуализация** - наглядное представление результатов
5. **Автоматизация** - минимум ручной работы для получения полного анализа

## 8. Использование

```python
# Анализ выбросов в метриках конверсии по городам
conversion_metrics = [
    "order2trip", "order2offer", "offer2assign",
    "assign2arrive", "arrive2trip"
]

conversion_outliers = detect_outliers_by_groups(
    df_metrics,
    metrics=conversion_metrics,
    group_by="city",
    method="iqr",
    threshold=1.5
)

# Вывод результатов
print_outliers_summary_table(conversion_outliers)

# Визуализация
visualize_outliers_by_groups(conversion_outliers, figsize=(16, 12))
```

## 9. Практическая ценность

Анализ метрик конверсии обеспечивает:
- **Раннее обнаружение проблем** в системе такси
- **Снижение ложных срабатываний** по сравнению с количественными метриками
- **Фокус на бизнес-критичных показателях** эффективности
- **Готовность к внедрению** в систему алертов

Этот подход гораздо более практичен для реальной работы с данными такси и создания системы мониторинга.
