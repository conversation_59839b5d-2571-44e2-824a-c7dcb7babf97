{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Рефакторенный анализ данных такси\n", "\n", "Этот notebook содержит автоматизированные функции для анализа данных такси с:\n", "- Автоматической загрузкой и валидацией данных\n", "- Переиспользуемыми функциями обработки и агрегации\n", "- Автоматизированной визуализацией\n", "- Системой детекции выбросов\n", "- Комплексной обработкой ошибок"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Импорт необходимых библиотек\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import warnings\n", "from typing import Dict, List, Optional, Union, Tuple, Any\n", "from pathlib import Path\n", "import seaborn as sns\n", "from scipy import stats\n", "\n", "# Настройка отображения\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "\n", "print(\"Библиотеки успешно импортированы\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Модуль загрузки и валидации данных"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_and_validate_taxi_data(file_path: str, \n", "                               required_columns: Optional[List[str]] = None,\n", "                               time_columns: Optional[List[str]] = None,\n", "                               time_format: str = \"%d.%m.%y %H:%M\",\n", "                               validate_data: bool = True) -> pd.DataFrame:\n", "    \"\"\"\n", "    Загружает и валидирует данные такси с автоматической обработкой временных колонок.\n", "    \n", "    Parameters:\n", "    -----------\n", "    file_path : str\n", "        Путь к файлу с данными\n", "    required_columns : List[str], optional\n", "        Список обязательных колонок для проверки\n", "    time_columns : List[str], optional\n", "        Список временных колонок для преобразования\n", "    time_format : str\n", "        Формат времени для преобразования\n", "    validate_data : bool\n", "        Выполнять ли валидацию данных\n", "        \n", "    Returns:\n", "    --------\n", "    pd.DataFrame\n", "        Загруженные и обработанные данные\n", "        \n", "    Raises:\n", "    -------\n", "    FileNotFoundError\n", "        Если файл не найден\n", "    ValueError\n", "        При ошибках валидации данных\n", "    \"\"\"\n", "    \n", "    # Значения по умолчанию\n", "    if required_columns is None:\n", "        required_columns = ['id_order', 'order_time', 'offer_time', 'assign_time', \n", "                          'arrive_time', 'trip_time', 'city']\n", "    \n", "    if time_columns is None:\n", "        time_columns = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']\n", "    \n", "    try:\n", "        # Загрузка данных\n", "        print(f\"Загрузка данных из {file_path}...\")\n", "        \n", "        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):\n", "            df = pd.read_excel(file_path)\n", "        elif file_path.endswith('.csv'):\n", "            df = pd.read_csv(file_path)\n", "        else:\n", "            raise ValueError(f\"Неподдерживаемый формат файла: {file_path}\")\n", "            \n", "        print(f\"Загружено {len(df)} строк и {len(df.columns)} колонок\")\n", "        \n", "        if validate_data:\n", "            # Проверка наличия обязательных колонок\n", "            missing_columns = set(required_columns) - set(df.columns)\n", "            if missing_columns:\n", "                raise ValueError(f\"Отсутствуют обязательные колонки: {missing_columns}\")\n", "            print(\"✓ Все обязательные колонки присутствуют\")\n", "            \n", "            # Проверка на дубликаты ID\n", "            if df['id_order'].duplicated().any():\n", "                warnings.warn(\"Обнаружены дублированные ID заказов\")\n", "            else:\n", "                print(\"✓ Дубликаты ID не обнаружены\")\n", "        \n", "        # Преобразование временных колонок\n", "        for col in time_columns:\n", "            if col in df.columns:\n", "                try:\n", "                    df[col] = pd.to_datetime(df[col], format=time_format)\n", "                    print(f\"✓ Колонка {col} преобразована в datetime\")\n", "                except Exception as e:\n", "                    print(f\"⚠ Ошибка преобразования колонки {col}: {e}\")\n", "                    # Попытка автоматического определения формата\n", "                    try:\n", "                        df[col] = pd.to_datetime(df[col], errors='coerce')\n", "                        print(f\"✓ Колонка {col} преобразована автоматически\")\n", "                    except:\n", "                        print(f\"✗ Не удалось преобразовать колонку {col}\")\n", "        \n", "        # Добавление производных колонок\n", "        if 'order_time' in df.columns and df['order_time'].dtype == 'datetime64[ns]':\n", "            df['day_order'] = df['order_time'].dt.day\n", "            df['hour_order'] = df['order_time'].dt.floor('h')\n", "            print(\"✓ Добавлены производные временные колонки\")\n", "        \n", "        # Статистика по пропущенным значениям\n", "        missing_stats = df.isnull().sum()\n", "        if missing_stats.sum() > 0:\n", "            print(\"\n", "Статистика пропущенных значений:\")\n", "            for col, count in missing_stats[missing_stats > 0].items():\n", "                percent = (count / len(df)) * 100\n", "                print(f\"  {col}: {count} ({percent:.1f}%)\")\n", "        else:\n", "            print(\"✓ Пропущенные значения не обнаружены\")\n", "            \n", "        print(f\"\n", "Данные успешно загружены и обработаны!\")\n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"Ошибка при загрузке данных: {e}\")\n", "        raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Модуль обработки и агрегации данных"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_taxi_metrics(df: pd.DataFrame,\n", "                          group_by: Union[str, List[str]] = 'day_order',\n", "                          metrics_config: Optional[Dict[str, str]] = None,\n", "                          conversion_metrics: bool = True) -> pd.DataFrame:\n", "    \"\"\"\n", "    Рассчитывает метрики такси с настраиваемыми параметрами группировки и агрегации.\n", "    \n", "    Parameters:\n", "    -----------\n", "    df : pd.DataFrame\n", "        Исходные данные такси\n", "    group_by : str or List[str]\n", "        Колонки для группировки данных\n", "    metrics_config : Dict[str, str], optional\n", "        Конфигурация метрик в формате {название_метрики: колонка_для_подсчета}\n", "    conversion_metrics : bool\n", "        Рассчитывать ли метрики конверсии\n", "        \n", "    Returns:\n", "    --------\n", "    pd.DataFrame\n", "        Агрегированные данные с метриками\n", "    \"\"\"\n", "    \n", "    # Конфигурация метрик по умолчанию\n", "    if metrics_config is None:\n", "        metrics_config = {\n", "            'cnt_order': 'id_order',\n", "            'cnt_offer': 'offer_time',\n", "            'cnt_assign': 'assign_time',\n", "            'cnt_arrive': 'arrive_time',\n", "            'cnt_trip': 'trip_time'\n", "        }\n", "    \n", "    print(f\"Расчет метрик с группировкой по: {group_by}\")\n", "    print(f\"Метрики для расчета: {list(metrics_config.keys())}\")\n", "    \n", "    try:\n", "        # Создание агрегации\n", "        agg_dict = {}\n", "        for metric_name, column in metrics_config.items():\n", "            if column in df.columns:\n", "                agg_dict[metric_name] = (column, 'count')\n", "            else:\n", "                print(f\"⚠ Колонка {column} не найдена, пропускаем метрику {metric_name}\")\n", "        \n", "        # Группировка и агрегация\n", "        df_grouped = df.groupby(group_by, as_index=False).agg(agg_dict)\n", "        \n", "        print(f\"✓ Создано {len(df_grouped)} агрегированных записей\")\n", "        \n", "        # Расчет метрик конверсии\n", "        if conversion_metrics and len(agg_dict) >= 2:\n", "            print(\"Расчет метрик конверсии...\")\n", "            \n", "            # Базовые конверсии\n", "            if 'cnt_order' in df_grouped.columns and 'cnt_trip' in df_grouped.columns:\n", "                df_grouped['order2trip'] = df_grouped['cnt_trip'] / df_grouped['cnt_order']\n", "                print(\"✓ order2trip (базовая конверсия)\")\n", "            \n", "            if 'cnt_order' in df_grouped.columns and 'cnt_offer' in df_grouped.columns:\n", "                df_grouped['order2offer'] = df_grouped['cnt_offer'] / df_grouped['cnt_order']\n", "                print(\"✓ order2offer\")\n", "            \n", "            if 'cnt_offer' in df_grouped.columns and 'cnt_assign' in df_grouped.columns:\n", "                df_grouped['offer2assign'] = df_grouped['cnt_assign'] / df_grouped['cnt_offer']\n", "                print(\"✓ offer2assign\")\n", "            \n", "            if 'cnt_assign' in df_grouped.columns and 'cnt_arrive' in df_grouped.columns:\n", "                df_grouped['assign2arrive'] = df_grouped['cnt_arrive'] / df_grouped['cnt_assign']\n", "                print(\"✓ assign2arrive\")\n", "            \n", "            if 'cnt_arrive' in df_grouped.columns and 'cnt_trip' in df_grouped.columns:\n", "                df_grouped['arrive2trip'] = df_grouped['cnt_trip'] / df_grouped['cnt_arrive']\n", "                print(\"✓ arrive2trip\")\n", "        \n", "        # Обработка бесконечных значений и NaN\n", "        df_grouped = df_grouped.replace([np.inf, -np.inf], np.nan)\n", "        \n", "        print(f\"\n", "Итоговые колонки: {list(df_grouped.columns)}\")\n", "        return df_grouped\n", "        \n", "    except Exception as e:\n", "        print(f\"Ошибка при расчете метрик: {e}\")\n", "        raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Мо<PERSON><PERSON><PERSON>ь автоматизированной визуализации"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_city_metrics(df_city: pd.DataFrame,\n", "                     metric_column: str,\n", "                     x_column: str = 'day_order',\n", "                     cities: Optional[List[str]] = None,\n", "                     title: Optional[str] = None,\n", "                     y_limit: Optional[Tuple[float, float]] = None,\n", "                     figsize: Tuple[int, int] = (12, 8),\n", "                     style: str = 'default',\n", "                     save_path: Optional[str] = None) -> None:\n", "    \"\"\"\n", "    Автоматизированное построение графиков метрик по городам.\n", "\n", "    Parameters:\n", "    -----------\n", "    df_city : pd.DataFrame\n", "        Данные с метриками по городам\n", "    metric_column : str\n", "        Название колонки с метрикой для отображения\n", "    x_column : str\n", "        Колонка для оси X\n", "    cities : List[str], optional\n", "        Список городов для отображения. Если None, отображаются все\n", "    title : str, optional\n", "        Заголовок графика\n", "    y_limit : <PERSON><PERSON>[float, float], optional\n", "        Ограничения по оси Y\n", "    figsize : <PERSON><PERSON>[int, int]\n", "        Размер фигуры\n", "    style : str\n", "        Стиль графика\n", "    save_path : str, optional\n", "        Путь для сохранения графика\n", "    \"\"\"\n", "\n", "    # Проверка наличия необходимых колонок\n", "    required_cols = [metric_column, x_column, 'city']\n", "    missing_cols = [col for col in required_cols if col not in df_city.columns]\n", "    if missing_cols:\n", "        raise ValueError(f\"Отсутствуют колонки: {missing_cols}\")\n", "\n", "    # Определение городов для отображения\n", "    if cities is None:\n", "        cities = df_city['city'].unique().tolist()\n", "        print(f\"Отображение всех городов: {cities}\")\n", "    else:\n", "        # Проверка наличия городов в данных\n", "        available_cities = df_city['city'].unique()\n", "        missing_cities = [city for city in cities if city not in available_cities]\n", "        if missing_cities:\n", "            print(f\"⚠ Города не найдены в данных: {missing_cities}\")\n", "            cities = [city for city in cities if city in available_cities]\n", "\n", "    if not cities:\n", "        raise ValueError(\"Нет доступных городов для отображения\")\n", "\n", "    # Настройка стиля\n", "    plt.style.use(style)\n", "\n", "    # Создание графика\n", "    plt.figure(figsize=figsize)\n", "\n", "    # Цветовая палитра\n", "    colors = plt.cm.Set1(np.linspace(0, 1, len(cities)))\n", "\n", "    for i, city in enumerate(cities):\n", "        city_data = df_city[df_city['city'] == city]\n", "\n", "        if len(city_data) == 0:\n", "            print(f\"⚠ Нет данных для города: {city}\")\n", "            continue\n", "\n", "        plt.plot(city_data[x_column], \n", "                city_data[metric_column], \n", "                label=city, \n", "                color=colors[i],\n", "                marker='o',\n", "                linewidth=2,\n", "                markersize=6)\n", "\n", "    # Настройка графика\n", "    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "    if title:\n", "        plt.title(title, fontsize=14, fontweight='bold')\n", "    else:\n", "        plt.title(f\"{metric_column} по городам\", fontsize=14, fontweight='bold')\n", "\n", "    plt.xlabel(x_column.replace('_', ' ').title(), fontsize=12)\n", "    plt.ylabel(metric_column.replace('_', ' ').title(), fontsize=12)\n", "\n", "    if y_limit:\n", "        plt.ylim(y_limit)\n", "\n", "    plt.tight_layout()\n", "\n", "    # Сохранение графика\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"График сохранен: {save_path}\")\n", "\n", "    plt.show()\n", "\n", "    # Статистика по метрике\n", "    print(f\"\\\n", "Статистика по метрике '{metric_column}':\")\n", "    for city in cities:\n", "        city_data = df_city[df_city['city'] == city][metric_column]\n", "        if len(city_data) > 0:\n", "            print(f\"  {city}: среднее={city_data.mean():.3f}, \"\n", "                  f\"мин={city_data.min():.3f}, макс={city_data.max():.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_comprehensive_dashboard(df_city: pd.DataFrame,\n", "                                 metrics: List[str],\n", "                                 cities: Optional[List[str]] = None,\n", "                                 x_column: str = 'day_order',\n", "                                 figsize: Tuple[int, int] = (20, 15)) -> None:\n", "    \"\"\"\n", "    Создает комплексный дашборд с несколькими метриками.\n", "\n", "    Parameters:\n", "    -----------\n", "    df_city : pd.DataFrame\n", "        Данные с метриками по городам\n", "    metrics : List[str]\n", "        Список метрик для отображения\n", "    cities : List[str], optional\n", "        Список городов для отображения\n", "    x_column : str\n", "        Колонка для оси X\n", "    figsize : <PERSON><PERSON>[int, int]\n", "        Размер фигуры\n", "    \"\"\"\n", "\n", "    if cities is None:\n", "        cities = df_city['city'].unique().tolist()\n", "\n", "    # Определение размера сетки\n", "    n_metrics = len(metrics)\n", "    n_cols = 2 if n_metrics > 1 else 1\n", "    n_rows = (n_metrics + n_cols - 1) // n_cols\n", "\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)\n", "    if n_metrics == 1:\n", "        axes = [axes]\n", "    elif n_rows == 1:\n", "        axes = axes.reshape(1, -1)\n", "\n", "    colors = plt.cm.Set1(np.linspace(0, 1, len(cities)))\n", "\n", "    for idx, metric in enumerate(metrics):\n", "        row = idx // n_cols\n", "        col = idx % n_cols\n", "        ax = axes[row, col] if n_rows > 1 else axes[col]\n", "\n", "        if metric not in df_city.columns:\n", "            ax.text(0.5, 0.5, f'Метрика {metric}\\\n", "не найдена', \n", "                   ha='center', va='center', transform=ax.transAxes)\n", "            ax.set_title(f\"Ошибка: {metric}\")\n", "            continue\n", "\n", "        for i, city in enumerate(cities):\n", "            city_data = df_city[df_city['city'] == city]\n", "            if len(city_data) > 0:\n", "                ax.plot(city_data[x_column], city_data[metric], \n", "                       label=city, color=colors[i], marker='o', linewidth=2)\n", "\n", "        ax.set_title(metric.replace('_', ' ').title(), fontweight='bold')\n", "        ax.set_xlabel(x_column.replace('_', ' ').title())\n", "        ax.set_ylabel(metric.replace('_', ' ').title())\n", "        ax.grid(True, alpha=0.3)\n", "        ax.legend()\n", "\n", "        # Установка лимитов для конверсий\n", "        if any(conv in metric for conv in ['2trip', '2offer', '2assign', '2arrive']):\n", "            ax.set_ylim([0, 1])\n", "\n", "    # Удаление пустых подграфиков\n", "    for idx in range(n_metrics, n_rows * n_cols):\n", "        row = idx // n_cols\n", "        col = idx % n_cols\n", "        if n_rows > 1:\n", "            fig.delaxes(axes[row, col])\n", "        else:\n", "            fig.delaxes(axes[col])\n", "\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Модуль детекции выбросов и аномалий"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_outliers(df: pd.DataFrame,\n", "                   columns: Optional[List[str]] = None,\n", "                   method: str = 'iqr',\n", "                   threshold: float = 1.5,\n", "                   z_threshold: float = 3.0,\n", "                   return_indices: bool = False) -> Union[pd.DataFrame, Dict[str, Any]]:\n", "    \"\"\"\n", "    Автоматическое обнаружение выбросов в данных с различными методами.\n", "\n", "    Parameters:\n", "    -----------\n", "    df : pd.DataFrame\n", "        Данные для анализа\n", "    columns : List[str], optional\n", "        Колонки для анализа. Если None, анализируются все числовые колонки\n", "    method : str\n", "        Метод детекции: 'iqr', 'zscore', 'modified_zscore', 'isolation_forest'\n", "    threshold : float\n", "        Пороговое значение для IQR метода\n", "    z_threshold : float\n", "        Пороговое значение для Z-score методов\n", "    return_indices : bool\n", "        Возвращать ли индексы выбросов\n", "\n", "    Returns:\n", "    --------\n", "    Union[pd.DataFrame, Dict]\n", "        Результаты детекции выбросов\n", "    \"\"\"\n", "\n", "    if columns is None:\n", "        # Автоматический выбор числовых колонок\n", "        columns = df.select_dtypes(include=[np.number]).columns.tolist()\n", "        print(f\"Анализ выбросов для колонок: {columns}\")\n", "\n", "    results = {}\n", "    all_outlier_indices = set()\n", "\n", "    for col in columns:\n", "        if col not in df.columns:\n", "            print(f\"⚠ Колонка {col} не найдена\")\n", "            continue\n", "\n", "        data = df[col].dropna()\n", "        if len(data) == 0:\n", "            print(f\"⚠ Колонка {col} не содержит данных\")\n", "            continue\n", "\n", "        outlier_indices = set()\n", "\n", "        if method == 'iqr':\n", "            Q1 = data.quantile(0.25)\n", "            Q3 = data.quantile(0.75)\n", "            IQR = Q3 - Q1\n", "            lower_bound = Q1 - threshold * IQR\n", "            upper_bound = Q3 + threshold * IQR\n", "            outlier_mask = (data < lower_bound) | (data > upper_bound)\n", "            outlier_indices = set(data[outlier_mask].index)\n", "\n", "        elif method == 'zscore':\n", "            z_scores = np.abs(stats.zscore(data))\n", "            outlier_mask = z_scores > z_threshold\n", "            outlier_indices = set(data[outlier_mask].index)\n", "\n", "        elif method == 'modified_zscore':\n", "            median = np.median(data)\n", "            mad = np.median(np.abs(data - median))\n", "            modified_z_scores = 0.6745 * (data - median) / mad\n", "            outlier_mask = np.abs(modified_z_scores) > z_threshold\n", "            outlier_indices = set(data[outlier_mask].index)\n", "\n", "        outlier_count = len(outlier_indices)\n", "        outlier_percent = (outlier_count / len(data)) * 100\n", "\n", "        results[col] = {\n", "            'count': outlier_count,\n", "            'percentage': outlier_percent,\n", "            'indices': list(outlier_indices) if return_indices else None,\n", "            'method': method,\n", "            'threshold': threshold if method == 'iqr' else z_threshold\n", "        }\n", "\n", "        all_outlier_indices.update(outlier_indices)\n", "\n", "        print(f\"Колонка {col}: {outlier_count} выбросов ({outlier_percent:.1f}%)\")\n", "\n", "    # Общая статистика\n", "    total_outliers = len(all_outlier_indices)\n", "    total_percent = (total_outliers / len(df)) * 100\n", "\n", "    summary = {\n", "        'total_outliers': total_outliers,\n", "        'total_percentage': total_percent,\n", "        'method': method,\n", "        'columns_analyzed': columns,\n", "        'outlier_indices': list(all_outlier_indices) if return_indices else None\n", "    }\n", "\n", "    print(f\"\\\n", "Общая статистика: {total_outliers} уникальных записей с выбросами ({total_percent:.1f}%)\")\n", "\n", "    return {'summary': summary, 'details': results}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_outliers_by_groups(df: pd.DataFrame,\n", "                             metrics: Optional[List[str]] = None,\n", "                             group_by: str = 'city',\n", "                             method: str = 'iqr',\n", "                             threshold: float = 1.5) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Анализ выбросов с группировкой по заданному столбцу.\n", "    \n", "    Parameters:\n", "    -----------\n", "    df : pd.DataFrame\n", "        Данные для анализа\n", "    metrics : List[str], optional\n", "        Список метрик для анализа. Если None, анализируются все числовые столбцы\n", "    group_by : str\n", "        Столбец для группировки (например, 'city')\n", "    method : str\n", "        Метод детекции выбросов ('iqr')\n", "    threshold : float\n", "        Пороговое значение для IQR метода\n", "        \n", "    Returns:\n", "    --------\n", "    Dict[str, Any]\n", "        Результаты анализа выбросов по группам\n", "    \"\"\"\n", "    \n", "    if group_by not in df.columns:\n", "        raise ValueError(f\"Столбец для группировки '{group_by}' не найден\")\n", "    \n", "    if metrics is None:\n", "        # Автоматический выбор числовых столбцов, исключая группировочный\n", "        metrics = [col for col in df.select_dtypes(include=[np.number]).columns \n", "                  if col != group_by]\n", "        print(f\"Анализ выбросов для метрик: {metrics}\")\n", "    \n", "    print(f\"Группировка по: {group_by}\")\n", "    print(f\"Метод: {method}, порог: {threshold}\")\n", "    \n", "    results = {}\n", "    summary_table = []\n", "    \n", "    groups = df[group_by].unique()\n", "    print(f\"Найдено групп: {len(groups)}\")\n", "    \n", "    for metric in metrics:\n", "        if metric not in df.columns:\n", "            print(f\"⚠ Метрика {metric} не найдена\")\n", "            continue\n", "            \n", "        metric_results = {}\n", "        \n", "        for group in groups:\n", "            group_data = df[df[group_by] == group][metric].dropna()\n", "            \n", "            if len(group_data) == 0:\n", "                continue\n", "                \n", "            # Применение IQR метода\n", "            Q1 = group_data.quantile(0.25)\n", "            Q3 = group_data.quantile(0.75)\n", "            IQR = Q3 - Q1\n", "            lower_bound = Q1 - threshold * IQR\n", "            upper_bound = Q3 + threshold * IQR\n", "            \n", "            outlier_mask = (group_data < lower_bound) | (group_data > upper_bound)\n", "            outlier_count = outlier_mask.sum()\n", "            outlier_percent = (outlier_count / len(group_data)) * 100\n", "            \n", "            metric_results[group] = {\n", "                'count': outlier_count,\n", "                'percentage': outlier_percent,\n", "                'total_records': len(group_data),\n", "                'lower_bound': lower_bound,\n", "                'upper_bound': upper_bound\n", "            }\n", "            \n", "            # Добавляем в сводную таблицу\n", "            summary_table.append({\n", "                'metric': metric,\n", "                'group': group,\n", "                'outliers_count': outlier_count,\n", "                'outliers_percentage': outlier_percent,\n", "                'total_records': len(group_data)\n", "            })\n", "        \n", "        results[metric] = metric_results\n", "    \n", "    return {\n", "        'results': results,\n", "        'summary_table': pd.DataFrame(summary_table),\n", "        'method': method,\n", "        'threshold': threshold,\n", "        'group_by': group_by\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_outliers_by_groups(outlier_results: Dict[str, Any],\n", "                                figsize: Tuple[int, int] = (15, 10)) -> None:\n", "    \"\"\"\n", "    Визуализация результатов анализа выбросов по группам.\n", "    \n", "    Parameters:\n", "    -----------\n", "    outlier_results : Dict[str, Any]\n", "        Результаты анализа выбросов от функции detect_outliers_by_groups\n", "    figsize : <PERSON><PERSON>[int, int]\n", "        Размер фигуры\n", "    \"\"\"\n", "    \n", "    summary_df = outlier_results['summary_table']\n", "    group_by = outlier_results['group_by']\n", "    \n", "    if len(summary_df) == 0:\n", "        print(\"Нет данных для визуализации\")\n", "        return\n", "    \n", "    # Создаем сводную таблицу для тепловой карты\n", "    pivot_count = summary_df.pivot(index='metric', columns='group', values='outliers_count')\n", "    pivot_percent = summary_df.pivot(index='metric', columns='group', values='outliers_percentage')\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=figsize)\n", "    fig.suptitle(f'Анализ выбросов по {group_by}', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Тепловая карта количества выбросов\n", "    sns.heatmap(pivot_count, annot=True, fmt='d', cmap='Reds', ax=axes[0,0])\n", "    axes[0,0].set_title('Количество выбросов')\n", "    axes[0,0].set_xlabel(group_by.capitalize())\n", "    axes[0,0].set_ylabel('Метрики')\n", "    \n", "    # 2. Тепловая карта процента выбросов\n", "    sns.heatmap(pivot_percent, annot=True, fmt='.1f', cmap='Oranges', ax=axes[0,1])\n", "    axes[0,1].set_title('Процент выбросов (%)')\n", "    axes[0,1].set_xlabel(group_by.capitalize())\n", "    axes[0,1].set_ylabel('Метрики')\n", "    \n", "    # 3. Столбчатая диаграмма общего количества выбросов по группам\n", "    group_totals = summary_df.groupby('group')['outliers_count'].sum().sort_values(ascending=False)\n", "    colors = plt.cm.Set3(np.linspace(0, 1, len(group_totals)))\n", "    bars = axes[1,0].bar(group_totals.index, group_totals.values, color=colors)\n", "    axes[1,0].set_title(f'Общее количество выбросов по {group_by}')\n", "    axes[1,0].set_xlabel(group_by.capitalize())\n", "    axes[1,0].set_ylabel('Количество выбросов')\n", "    axes[1,0].tick_params(axis='x', rotation=45)\n", "    \n", "    # Добавляем значения на столбцы\n", "    for bar in bars:\n", "        height = bar.get_height()\n", "        axes[1,0].text(bar.get_x() + bar.get_width()/2., height,\n", "                      f'{int(height)}', ha='center', va='bottom')\n", "    \n", "    # 4. Столбчатая диаграмма по метрикам\n", "    metric_totals = summary_df.groupby('metric')['outliers_count'].sum().sort_values(ascending=False)\n", "    colors = plt.cm.Set2(np.linspace(0, 1, len(metric_totals)))\n", "    bars = axes[1,1].bar(metric_totals.index, metric_totals.values, color=colors)\n", "    axes[1,1].set_title('Общее количество выбросов по метрикам')\n", "    axes[1,1].set_xlabel('Метрики')\n", "    axes[1,1].set_ylabel('Количество выбросов')\n", "    axes[1,1].tick_params(axis='x', rotation=45)\n", "    \n", "    # Добавляем значения на столбцы\n", "    for bar in bars:\n", "        height = bar.get_height()\n", "        axes[1,1].text(bar.get_x() + bar.get_width()/2., height,\n", "                      f'{int(height)}', ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_outliers_summary_table(outlier_results: Dict[str, Any]) -> None:\n", "    \"\"\"\n", "    Выводит сводную таблицу результатов анализа выбросов.\n", "    \n", "    Parameters:\n", "    -----------\n", "    outlier_results : Dict[str, Any]\n", "        Результаты анализа выбросов от функции detect_outliers_by_groups\n", "    \"\"\"\n", "    \n", "    summary_df = outlier_results['summary_table']\n", "    group_by = outlier_results['group_by']\n", "    method = outlier_results['method']\n", "    threshold = outlier_results['threshold']\n", "    \n", "    print(f\"\\n=== СВОДНАЯ ТАБЛИЦА ВЫБРОСОВ ===\")\n", "    print(f\"Метод: {method.upper()}, порог: {threshold}\")\n", "    print(f\"Группировка по: {group_by}\\n\")\n", "    \n", "    if len(summary_df) == 0:\n", "        print(\"Нет данных для отображения\")\n", "        return\n", "    \n", "    # Создаем красивую таблицу\n", "    display_df = summary_df.copy()\n", "    display_df['outliers_percentage'] = display_df['outliers_percentage'].round(2)\n", "    display_df = display_df.rename(columns={\n", "        'metric': 'Метрика',\n", "        'group': group_by.capitalize(),\n", "        'outliers_count': 'Выбросы',\n", "        'outliers_percentage': 'Процент (%)',\n", "        'total_records': 'Всего записей'\n", "    })\n", "    \n", "    print(display_df.to_string(index=False))\n", "    \n", "    # Общая статистика\n", "    total_outliers = summary_df['outliers_count'].sum()\n", "    total_records = summary_df['total_records'].sum()\n", "    overall_percent = (total_outliers / total_records * 100) if total_records > 0 else 0\n", "    \n", "    print(f\"\\n=== ОБЩАЯ СТАТИСТИКА ===\")\n", "    print(f\"Всего выбросов: {total_outliers}\")\n", "    print(f\"Всего записей: {total_records}\")\n", "    print(f\"Общий процент выбросов: {overall_percent:.2f}%\")\n", "    \n", "    # Статистика по группам\n", "    print(f\"\\n=== СТАТИСТИКА ПО {group_by.upper()} ===\")\n", "    group_stats = summary_df.groupby('group').agg({\n", "        'outliers_count': 'sum',\n", "        'total_records': 'sum'\n", "    })\n", "    group_stats['percentage'] = (group_stats['outliers_count'] / group_stats['total_records'] * 100).round(2)\n", "    group_stats = group_stats.sort_values('outliers_count', ascending=False)\n", "    \n", "    for group, row in group_stats.iterrows():\n", "        print(f\"{group}: {row['outliers_count']} выбросов ({row['percentage']:.2f}%) из {row['total_records']} записей\")\n", "    \n", "    # Статистика по метрикам\n", "    print(f\"\\n=== СТАТИСТИКА ПО МЕТРИКАМ ===\")\n", "    metric_stats = summary_df.groupby('metric').agg({\n", "        'outliers_count': 'sum',\n", "        'total_records': 'sum'\n", "    })\n", "    metric_stats['percentage'] = (metric_stats['outliers_count'] / metric_stats['total_records'] * 100).round(2)\n", "    metric_stats = metric_stats.sort_values('outliers_count', ascending=False)\n", "    \n", "    for metric, row in metric_stats.iterrows():\n", "        print(f\"{metric}: {row['outliers_count']} выбросов ({row['percentage']:.2f}%) из {row['total_records']} записей\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_conversion_anomalies(df_metrics: pd.DataFrame,\n", "                                conversion_columns: Optional[List[str]] = None,\n", "                                threshold_low: float = 0.1,\n", "                                threshold_high: float = 0.9,\n", "                                group_by: str = 'city') -> Dict[str, Any]:\n", "    \"\"\"\n", "    Ана<PERSON>из аномалий в метриках конверсии.\n", "\n", "    Parameters:\n", "    -----------\n", "    df_metrics : pd.DataFrame\n", "        Данные с метриками конверсии\n", "    conversion_columns : List[str], optional\n", "        Колонки с конверсиями для анализа\n", "    threshold_low : float\n", "        Нижний порог для определения аномально низких конверсий\n", "    threshold_high : float\n", "        Верхний порог для определения аномально высоких конверсий\n", "    group_by : str\n", "        Колонка для группировки анализа\n", "\n", "    Returns:\n", "    --------\n", "    Dict[str, Any]\n", "        Результаты анализа аномалий конверсии\n", "    \"\"\"\n", "\n", "    if conversion_columns is None:\n", "        # Автоматический поиск колонок с конверсиями\n", "        conversion_columns = [col for col in df_metrics.columns \n", "                            if '2' in col and any(x in col for x in ['trip', 'offer', 'assign', 'arrive'])]\n", "        print(f\"Найдены колонки конверсии: {conversion_columns}\")\n", "\n", "    anomalies = {}\n", "\n", "    for conv_col in conversion_columns:\n", "        if conv_col not in df_metrics.columns:\n", "            print(f\"⚠ Колонка {conv_col} не найдена\")\n", "            continue\n", "\n", "        col_anomalies = {\n", "            'low_conversion': [],\n", "            'high_conversion': [],\n", "            'zero_conversion': [],\n", "            'statistics': {}\n", "        }\n", "\n", "        # Анализ по группам\n", "        if group_by in df_metrics.columns:\n", "            for group_value in df_metrics[group_by].unique():\n", "                group_data = df_metrics[df_metrics[group_by] == group_value][conv_col]\n", "                group_data = group_data.dropna()\n", "\n", "                if len(group_data) == 0:\n", "                    continue\n", "\n", "                # Статистика по группе\n", "                mean_conv = group_data.mean()\n", "                min_conv = group_data.min()\n", "                max_conv = group_data.max()\n", "                std_conv = group_data.std()\n", "\n", "                col_anomalies['statistics'][group_value] = {\n", "                    'mean': mean_conv,\n", "                    'min': min_conv,\n", "                    'max': max_conv,\n", "                    'std': std_conv,\n", "                    'count': len(group_data)\n", "                }\n", "\n", "                # Поиск аномалий\n", "                low_conv_count = (group_data < threshold_low).sum()\n", "                high_conv_count = (group_data > threshold_high).sum()\n", "                zero_conv_count = (group_data == 0).sum()\n", "\n", "                if low_conv_count > 0:\n", "                    col_anomalies['low_conversion'].append({\n", "                        'group': group_value,\n", "                        'count': low_conv_count,\n", "                        'percentage': (low_conv_count / len(group_data)) * 100\n", "                    })\n", "\n", "                if high_conv_count > 0:\n", "                    col_anomalies['high_conversion'].append({\n", "                        'group': group_value,\n", "                        'count': high_conv_count,\n", "                        'percentage': (high_conv_count / len(group_data)) * 100\n", "                    })\n", "\n", "                if zero_conv_count > 0:\n", "                    col_anomalies['zero_conversion'].append({\n", "                        'group': group_value,\n", "                        'count': zero_conv_count,\n", "                        'percentage': (zero_conv_count / len(group_data)) * 100\n", "                    })\n", "\n", "        anomalies[conv_col] = col_anomalies\n", "\n", "        # Вывод результатов\n", "        print(f\"\\\n", "Анализ аномалий для {conv_col}:\")\n", "        if col_anomalies['low_conversion']:\n", "            print(f\"  Низкие конверсии (< {threshold_low}): {len(col_anomalies['low_conversion'])} групп\")\n", "        if col_anomalies['high_conversion']:\n", "            print(f\"  Высокие конверсии (> {threshold_high}): {len(col_anomalies['high_conversion'])} групп\")\n", "        if col_anomalies['zero_conversion']:\n", "            print(f\"  Нулевые конверсии: {len(col_anomalies['zero_conversion'])} групп\")\n", "\n", "    return anomalies"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Демонстрация использования автоматизированных функций"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 Загрузка и валидация данных"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Загрузка данных с автоматической валидацией\n", "df = load_and_validate_taxi_data('taxi_data.xlsx')\n", "\n", "# Отображение основной информации о данных\n", "print(f\"\\\n", "Основная информация о данных:\")\n", "print(f\"Размер данных: {df.shape}\")\n", "print(f\"Колонки: {list(df.columns)}\")\n", "print(f\"Уникальные города: {df['city'].unique()}\")\n", "print(f\"Период данных: с {df['order_time'].min()} по {df['order_time'].max()}\")\n", "\n", "# Отображение первых строк\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Расчет метрик по дням"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Расчет метрик с группировкой по дням\n", "df_gr_dyn = calculate_taxi_metrics(df, group_by='day_order')\n", "\n", "print(\"\\\n", "Метрики по дням:\")\n", "df_gr_dyn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Расчет метрик по дням и городам"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Расчет метрик с группировкой по дням и городам\n", "df_gr_dyn_city = calculate_taxi_metrics(df, group_by=['day_order', 'city'])\n", "\n", "print(\"\\\n", "Метрики по дням и городам:\")\n", "df_gr_dyn_city.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.4 Автоматизированная визуализация"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# График количества заказов по городам\n", "plot_city_metrics(df_gr_dyn_city, \n", "                 metric_column='cnt_order',\n", "                 title=\"Количество заказов\",\n", "                 cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# График базовой конверсии Order2Trip\n", "plot_city_metrics(df_gr_dyn_city, \n", "                 metric_column='order2trip',\n", "                 title=\"Order2Trip - Базовая конверсия\",\n", "                 y_limit=(0, 1),\n", "                 cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# График конверсии Order2Offer\n", "plot_city_metrics(df_gr_dyn_city, \n", "                 metric_column='order2offer',\n", "                 title=\"Order2Offer - Конверсия из заказа в предложение\",\n", "                 y_limit=(0, 1),\n", "                 cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Гра<PERSON>и<PERSON> конверсии Offer2Assign\n", "plot_city_metrics(df_gr_dyn_city, \n", "                 metric_column='offer2assign',\n", "                 title=\"Offer2Assign - Конверсия из предложения в назначение\",\n", "                 y_limit=(0, 1),\n", "                 cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Гра<PERSON>ик конверсии Assign2Arrive\n", "plot_city_metrics(df_gr_dyn_city, \n", "                 metric_column='assign2arrive',\n", "                 title=\"Assign2Arrive - Конверсия из назначения в прибытие\",\n", "                 y_limit=(0, 1),\n", "                 cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# График конверсии Arrive2Trip\n", "plot_city_metrics(df_gr_dyn_city, \n", "                 metric_column='arrive2trip',\n", "                 title=\"Arrive2Trip - Конверсия из прибытия в завершение поездки\",\n", "                 y_limit=(0, 1),\n", "                 cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.5 Комплексный дашборд"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Создание комплексного дашборда со всеми метриками конверсии\n", "conversion_metrics = ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip']\n", "\n", "create_comprehensive_dashboard(df_gr_dyn_city, \n", "                             metrics=conversion_metrics,\n", "                             cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'],\n", "                             figsize=(20, 15))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.6 Детекция выбросов в исходных данных"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Анализ выбросов в исходных данных\n", "print(\"=== АНАЛИЗ ВЫБРОСОВ В ИСХОДНЫХ ДАННЫХ ===\")\n", "\n", "# Выбираем числовые колонки для анализа\n", "numeric_columns = ['day_order']  # Можно добавить другие числовые колонки\n", "\n", "outlier_results = detect_outliers(df, \n", "                                columns=numeric_columns,\n", "                                method='iqr',\n", "                                threshold=1.5,\n", "                                return_indices=True)\n", "\n", "print(\"\\\n", "Результаты детекции выбросов:\")\n", "for col, details in outlier_results['details'].items():\n", "    print(f\"{col}: {details['count']} выбросов ({details['percentage']:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.7 Расширенный анализ выбросов по городам"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Расширенный анализ выбросов с группировкой по городам\n", "print(\"=== РАСШИРЕННЫЙ АНАЛИЗ ВЫБРОСОВ ПО ГОРОДАМ ===\")\n", "\n", "# Определяем числовые метрики для анализа\n", "count_metrics = ['cnt_order', 'cnt_offer', 'cnt_assign', 'cnt_arrive', 'cnt_trip']\n", "conversion_metrics = ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip']\n", "\n", "# Анализ выбросов в метриках количества\n", "print(\"\\n1. Анализ выбросов в метриках количества:\")\n", "count_outliers = detect_outliers_by_groups(\n", "    df_gr_dyn_city,\n", "    metrics=count_metrics,\n", "    group_by='city',\n", "    method='iqr',\n", "    threshold=1.5\n", ")\n", "\n", "# Выводим сводную таблицу\n", "print_outliers_summary_table(count_outliers)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Визуализация выбросов в метриках количества\n", "print(\"\\nВизуализация выбросов в метриках количества:\")\n", "visualize_outliers_by_groups(count_outliers, figsize=(16, 12))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Анализ выбросов в метриках конверсии\n", "print(\"\\n2. Анализ выбросов в метриках конверсии:\")\n", "conversion_outliers = detect_outliers_by_groups(\n", "    df_gr_dyn_city,\n", "    metrics=conversion_metrics,\n", "    group_by='city',\n", "    method='iqr',\n", "    threshold=1.5\n", ")\n", "\n", "# Выводим сводную таблицу\n", "print_outliers_summary_table(conversion_outliers)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Визуализация выбросов в метриках конверсии\n", "print(\"\\nВизуализация выбросов в метриках конверсии:\")\n", "visualize_outliers_by_groups(conversion_outliers, figsize=(16, 12))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Комплексный анализ всех числовых метрик\n", "print(\"\\n3. Комплексный анализ всех числовых метрик:\")\n", "all_metrics = count_metrics + conversion_metrics\n", "\n", "all_outliers = detect_outliers_by_groups(\n", "    df_gr_dyn_city,\n", "    metrics=all_metrics,\n", "    group_by='city',\n", "    method='iqr',\n", "    threshold=1.5\n", ")\n", "\n", "# Выводим сводную таблицу\n", "print_outliers_summary_table(all_outliers)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Детальный анализ выбросов по каждому городу\n", "print(\"\\n=== ДЕТАЛЬНЫЙ АНАЛИЗ ПО ГОРОДАМ ===\")\n", "\n", "for city in df_gr_dyn_city['city'].unique():\n", "    print(f\"\\n--- {city} ---\")\n", "    city_data = df_gr_dyn_city[df_gr_dyn_city['city'] == city]\n", "    \n", "    # Анализ выбросов для данного города\n", "    city_outliers = detect_outliers(\n", "        city_data,\n", "        columns=all_metrics,\n", "        method='iqr',\n", "        threshold=1.5,\n", "        return_indices=False\n", "    )\n", "    \n", "    # Выводим топ-3 метрики с наибольшим количеством выбросов\n", "    sorted_metrics = sorted(city_outliers['details'].items(), \n", "                          key=lambda x: x[1]['count'], reverse=True)[:3]\n", "    \n", "    print(f\"Топ-3 метрики с выбросами:\")\n", "    for metric, details in sorted_metrics:\n", "        if details['count'] > 0:\n", "            print(f\"  {metric}: {details['count']} выбросов ({details['percentage']:.1f}%)\")\n", "    \n", "    if not any(details['count'] > 0 for _, details in sorted_metrics):\n", "        print(\"  Выбросы не обнаружены\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.8 Анализ аномалий в конверсиях"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Анализ аномалий в метриках конверсии\n", "print(\"=== АНАЛИЗ АНОМАЛИЙ В КОНВЕРСИЯХ ===\")\n", "\n", "conversion_anomalies = analyze_conversion_anomalies(df_gr_dyn_city,\n", "                                                  threshold_low=0.1,\n", "                                                  threshold_high=0.9,\n", "                                                  group_by='city')\n", "\n", "# Детальный анализ аномалий\n", "print(\"\\\n", "=== ДЕТАЛЬНЫЙ АНАЛИЗ АНОМАЛИЙ ===\")\n", "for metric, anomalies in conversion_anomalies.items():\n", "    print(f\"\\\n", "Метрика: {metric}\")\n", "\n", "    if anomalies['low_conversion']:\n", "        print(\"  Группы с низкими конверсиями:\")\n", "        for item in anomalies['low_conversion']:\n", "            print(f\"    {item['group']}: {item['count']} случаев ({item['percentage']:.1f}%)\")\n", "\n", "    if anomalies['high_conversion']:\n", "        print(\"  Группы с высокими конверсиями:\")\n", "        for item in anomalies['high_conversion']:\n", "            print(f\"    {item['group']}: {item['count']} случаев ({item['percentage']:.1f}%)\")\n", "\n", "    if anomalies['zero_conversion']:\n", "        print(\"  Группы с нулевыми конверсиями:\")\n", "        for item in anomalies['zero_conversion']:\n", "            print(f\"    {item['group']}: {item['count']} случаев ({item['percentage']:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.8 Сводная статистика и выводы"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Сводная статистика по всем метрикам\n", "print(\"=== СВОДНАЯ СТАТИСТИКА ===\")\n", "\n", "print(f\"Общее количество записей: {len(df)}\")\n", "print(f\"Количество уникальных заказов: {df['id_order'].nunique()}\")\n", "print(f\"Количество городов: {df['city'].nunique()}\")\n", "print(f\"Период анализа: {df['day_order'].min()} - {df['day_order'].max()} день месяца\")\n", "\n", "# Средние конверсии по городам\n", "print(\"\\\n", "Средние конверсии по городам:\")\n", "for city in df['city'].unique():\n", "    city_data = df_gr_dyn_city[df_gr_dyn_city['city'] == city]\n", "    if len(city_data) > 0:\n", "        print(f\"\\\n", "{city}:\")\n", "        for metric in ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip']:\n", "            if metric in city_data.columns:\n", "                avg_conv = city_data[metric].mean()\n", "                print(f\"  {metric}: {avg_conv:.3f}\")\n", "\n", "print(\"\\\n", "=== АНАЛИЗ ЗАВЕРШЕН ===\")\n", "print(\"Все оригинальные результаты воспроизведены с помощью автоматизированных функций!\")\n", "print(\"Преимущества рефакторинга:\")\n", "print(\"- Значительное сокращение дублированного кода\")\n", "print(\"- Переиспользуемые функции с настраиваемыми параметрами\")\n", "print(\"- Автоматическая валидация и обработка ошибок\")\n", "print(\"- Система детекции выбросов и аномалий\")\n", "print(\"- Расширенный анализ выбросов с группировкой по городам\")\n", "print(\"- Визуализация и детальная статистика выбросов\")\n", "print(\"- Легкая расширяемость для новых типов анализа\")\n", "\n", "print(\"\\n=== ВЫВОДЫ ПО АНАЛИЗУ ВЫБРОСОВ ===\")\n", "print(\"1. Реализован расширенный анализ выбросов с группировкой по городам\")\n", "print(\"2. Анализ применяется ко всем числовым метрикам (cnt_order, cnt_offer, sum_amount и др.)\")\n", "print(\"3. Выбросы определяются относительно каждого города отдельно\")\n", "print(\"4. Используется IQR метод как наиболее подходящий для данного типа данных\")\n", "print(\"5. Результаты представлены в виде:\")\n", "print(\"   - Сводных таблиц с количеством и процентом выбросов\")\n", "print(\"   - Тепловых карт для визуализации распределения\")\n", "print(\"   - Столбчатых диаграмм по городам и метрикам\")\n", "print(\"   - Детального анализа по каждому городу\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Демонстрация расширенного анализа выбросов"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Демонстрация работы расширенного анализа выбросов\n", "print(\"=== ДЕМОНСТРАЦИЯ РАСШИРЕННОГО АНАЛИЗА ВЫБРОСОВ ===\")\n", "\n", "# Загружаем данные\n", "try:\n", "    df_demo = load_and_validate_taxi_data('taxi_data.csv')\n", "    print(f\"Данные загружены: {df_demo.shape}\")\n", "    \n", "    # Рассчитываем метрики\n", "    df_demo_metrics = calculate_taxi_metrics(df_demo, group_by=['day_order', 'city'])\n", "    print(f\"Метрики рассчитаны: {df_demo_metrics.shape}\")\n", "    \n", "    # Показываем структуру данных\n", "    print(\"\\nСтруктура данных с метриками:\")\n", "    print(df_demo_metrics.head())\n", "    \n", "    # Запускаем расширенный анализ выбросов\n", "    print(\"\\n=== ЗАПУСК РАСШИРЕННОГО АНАЛИЗА ===\")\n", "    \n", "    # Определяем метрики для анализа\n", "    demo_count_metrics = ['cnt_order', 'cnt_offer', 'cnt_assign', 'cnt_arrive', 'cnt_trip']\n", "    demo_conversion_metrics = ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip']\n", "    \n", "    # Анализ выбросов в метриках количества\n", "    print(\"\\n1. Анализ выбросов в метриках количества по городам:\")\n", "    demo_count_outliers = detect_outliers_by_groups(\n", "        df_demo_metrics,\n", "        metrics=demo_count_metrics,\n", "        group_by='city',\n", "        method='iqr',\n", "        threshold=1.5\n", "    )\n", "    \n", "    # Выводим результаты\n", "    print_outliers_summary_table(demo_count_outliers)\n", "    \n", "    # Визуализация\n", "    print(\"\\nВизуализация результатов:\")\n", "    visualize_outliers_by_groups(demo_count_outliers, figsize=(16, 12))\n", "    \n", "    print(\"\\n=== ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА ===\")\n", "    print(\"Расширенный анализ выбросов успешно применен к данным!\")\n", "    \n", "except Exception as e:\n", "    print(f\"Ошибка при демонстрации: {e}\")\n", "    print(\"Убедитесь, что файл taxi_data.csv находится в рабочей директории\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}